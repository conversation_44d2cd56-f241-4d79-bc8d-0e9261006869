package models

import (
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"time"
)

// 心愿单
type Wish struct {
	Model

	UserId          uint `gorm:"index"`
	Title           string
	Cover           string
	From            string                  // 出发地
	To              string                  // 目的地
	ToZoneId        uint                    // 目的地城市
	ToPoi           string                  //lng,lat
	BudgetType      constmap.WishBudgetType // 预算类型
	Budget          string                  // 预算
	TotalPeople     int                     // 总人数
	DepartDate      string                  // 出发时间(2025-01-01/2025-01)
	ReturnDate      string                  //结束时间
	Deadline        time.Time               `gorm:"index"` // 截止时间
	OpenScope       int                     //是否公开 1是 2否
	State           constmap.WishState
	RejectReason    string
	MemberDesc      string                   `gorm:"size:1024"` // 成员描述
	WishDesc        string                   `gorm:"size:1024"` // 心愿描述
	TagIds          utils.Marshaller[[]uint] `gorm:"size:1024"`
	PlanId          uint
	PlanDisableEdit int `gorm:"type:tinyint;default:2"` //结束编辑

	Todos   []WishTodo
	Members []WishMember
	Medias  []WishMedia
	Ext     WishExt
}

type WishExt struct {
	Model

	WishId    uint   `gorm:"index"`
	RiskCheck string `gorm:"type:text"`
}

// 必做的事
type WishTodo struct {
	Model

	WishId uint `gorm:"index"`
	Todo   string
	IsMust int //是否必做
}

// 心愿标签
type WishTag struct {
	Model

	Tag       string `gorm:"unique;index:idx_tag"`
	WishCount int    `gorm:"index:idx_tag;default:0"`
}

// 心愿成员
type WishMember struct {
	Model

	WishId      uint `gorm:"uniqueIndex:wishMember"`
	UserId      uint `gorm:"uniqueIndex:wishMember"`
	RealName    string
	Phone       string
	IsOwner     int `gorm:"type:tinyint"`
	Budget      string
	Remark      string
	State       constmap.WishMemberState
	FollowState constmap.WishFollowState
}

// 心愿关联媒体资源
type WishMedia struct {
	Model

	WishId    uint `gorm:"index"`
	MediaType constmap.WishMediaType
	Size      int64 //媒体资源大小(字节)
	ResUrl    string
}

// 心愿评论
type WishComment struct {
	Model

	WishId          uint                      `gorm:"index:wishComment"`
	UserId          uint                      `gorm:"index:wishComment"`
	ParentCommentId uint                      `gorm:"index"` //父级评论id,只保留2级评论,要保证父级评论没有上级评论
	ReplyCommentId  uint                      //回复一条回复时的被回复id
	ReplyUserId     uint                      //回复一条回复时的被回复记录所属用户id
	Content         string                    `gorm:"type:text"`
	State           constmap.WishCommentState `gorm:"type:tinyint"`
	IsPrivate       int                       `gorm:"type:tinyint"` //是否私密
}
