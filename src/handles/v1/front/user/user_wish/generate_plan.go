package user_wish

import (
	"context"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/components/business/wish_biz"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 生成行程
func GeneratePlan(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint `form:"wish_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, err := business.GetFrontLoginUser(ctx)
	if err != nil {
		return nil, err
	}

	// 1. 查询心愿单信息，预加载想做的事
	wish := &models.Wish{}
	if err := db.Preload("Todos").Take(wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 2. 验证权限：只有发起人可以生成行程
	if wish.UserId != session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只有发起人可以生成行程")
	}

	// 3. 验证心愿单状态：只有已达成的心愿单可以生成行程
	if wish.State != constmap.WishStateSuccess {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只有已达成的心愿单可以生成行程")
	}

	// 4. 检查是否已经生成过行程
	var existingPlan models.Plan
	err = db.Where("wish_id = ? AND user_id = ?", in.WishId, session.UserId).First(&existingPlan).Error
	if err == nil {
		// 已经存在行程，返回现有行程信息
		var out struct {
			WishId uint   `json:"wish_id"`
			PlanId uint   `json:"plan_id"`
			Status string `json:"status"`
		}
		out.WishId = in.WishId
		out.PlanId = existingPlan.ID
		out.Status = "already_exists"
		return out, nil
	}

	// 5. 查询心愿单的所有评论内容，用于生成行程提示词
	var comments []models.WishComment
	db.Where("wish_id = ? AND state = ?", in.WishId, constmap.WishCommentStateApproved).
		Order("created_at ASC").
		Find(&comments)

	// 6. 构建评论内容字符串
	var commentContents []string
	for _, comment := range comments {
		if comment.Content != "" {
			commentContents = append(commentContents, comment.Content)
		}
	}

	// 7. 构建AI提示信息
	promptText := wish_biz.BuildWishPrompt(&wish, wish.Todos)
	if len(commentContents) > 0 {
		promptText += fmt.Sprintf("\n成员建议：%s", strings.Join(commentContents, "; "))
	}
	my_logger.Infof("心愿单AI提示信息", zap.Uint("wishId", in.WishId), zap.String("prompt", promptText))

	// 8. 调用AI生成行程计划
	aiCtx := context.Background()
	travelInfo, err := my_dify.MakeWishPlan(aiCtx, db, promptText)
	if err != nil {
		my_logger.Errorf("生成行程计划失败", zap.Uint("wishId", in.WishId), zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "AI生成行程失败，请稍后重试")
	}

	my_logger.Infof("成功生成行程计划", zap.Uint("wishId", in.WishId), zap.String("travelInfo", travelInfo))

	// 9. 解析AI生成的行程内容
	aiReqId := fmt.Sprintf("dify:wish%d:%s", in.WishId, utils.UUID())
	chatMsg := &ai.ChatCompletionResponse{
		RequestId: aiReqId,
		Content:   travelInfo,
		Provider:  "dify",
	}

	_ = plan_biz.SetDetailCc(chatMsg)

	// 10. 使用AI解析器解析行程内容
	aiModel := ai.NewDify()
	journeyData, err := plan_biz.ParseAIJourney(db, aiModel, chatMsg)
	if err != nil || len(journeyData.List) == 0 || !strings.Contains(travelInfo, "【行程安排】") {
		my_logger.Errorf("解析行程内容失败", zap.Uint("wishId", in.WishId), zap.String("aiReqid", aiReqId), zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "解析行程内容失败，请稍后重试")
	}

	my_logger.Infof("成功解析行程内容", zap.Uint("wishId", in.WishId), zap.String("title", journeyData.Title))

	// 11. 使用公共方法构建完整的Plan结构
	plan, err := plan_asm.BuildSubmitPlan(db, plan_asm.SavePlanReq{
		UserId: session.UserId,
		State:  constmap.Enable,
	}, chatMsg)
	if err != nil {
		my_logger.Errorf("构建行程失败", zap.Uint("wishId", in.WishId), zap.Uint("userId", session.UserId), zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "构建行程失败，请稍后重试")
	}

	// 12. 设置心愿单关联
	plan.WishId = in.WishId

	// 13. 保存行程到数据库
	if err := plan_asm.SubmitPlan(db, plan); err != nil {
		my_logger.Errorf("保存行程失败", zap.Uint("wishId", in.WishId), zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "保存行程失败，请稍后重试")
	}

	my_logger.Infof("成功生成并保存行程", zap.Uint("wishId", in.WishId), zap.Uint("planId", plan.ID))

	// 14. 组装返回数据
	var out struct {
		WishId uint   `json:"wish_id"`
		PlanId uint   `json:"plan_id"`
		Status string `json:"status"`
	}
	out.WishId = in.WishId
	out.PlanId = plan.ID
	out.Status = "created"

	return out, nil
}
