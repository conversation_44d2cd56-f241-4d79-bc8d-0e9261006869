package user_wish

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 邀请同行
func InviteMember(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint `form:"wish_id" binding:"required"`
		UserId uint `form:"user_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, err := business.GetFrontLoginUser(ctx)
	if err != nil {
		return nil, err
	}

	if in.UserId == session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	// 1. 验证心愿单是否存在并检查权限
	wish := new(models.Wish)
	if err := db.Take(wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 2. 验证只有发起人可以邀请同行
	if wish.UserId != session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只有发起人可以邀请同行")
	}

	// 3. 验证心愿单状态（只有进行中或已达成的心愿单可以邀请）
	if wish.State != constmap.WishStateProcessing {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "当前心愿单状态不可邀请同行")
	}

	// 5. 验证用户是否评论过该心愿单
	var commentCount int64
	err = db.Model(&models.WishComment{}).
		Where("wish_id = ? AND user_id = ? AND state = ?",
			in.WishId, in.UserId, constmap.WishCommentStateApproved).
		Count(&commentCount).Error
	if err != nil {
		return nil, utils.NewError(err)
	}
	if commentCount == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只能邀请评论过该心愿单的用户")
	}

	// 6. 检查用户是否已经是成员或邀请状态
	var existingMember models.WishMember
	db.Where("wish_id = ? AND user_id = ?", in.WishId, in.UserId).
		First(&existingMember)

	if existingMember.ID > 0 && existingMember.State != constmap.WishMemberStateKickOff {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "成员状态不符合要求")
	}

	// 7. 添加互斥锁防止并发邀请
	mutexKey := fmt.Sprintf(constmap.RKMutex, "wishInvite", fmt.Sprintf("%d:%d", in.WishId, in.UserId))
	unlocker, err := my_cache.Mutex(mutexKey, constmap.TimeDur1m)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "操作过快，请稍后重试")
	}
	defer unlocker()

	// 8. 使用事务创建邀请记录和发送消息
	var member *models.WishMember
	err = db.Transaction(func(tx *gorm.DB) error {
		if existingMember.ID > 0 && existingMember.State == constmap.WishMemberStateKickOff {
			// 更新已被踢出的成员记录
			member = &existingMember
			if err := tx.Model(member).Updates(models.WishMember{State: constmap.WishMemberStateInviteWait}).Error; err != nil {
				return err
			}
		} else {
			// 创建新的邀请记录
			member = &models.WishMember{
				WishId:      in.WishId,
				UserId:      in.UserId,
				IsOwner:     constmap.Disable,                   // 非创建者
				State:       constmap.WishMemberStateInviteWait, // 邀请中，待确认
				FollowState: constmap.WishFollowStateDefault,
			}
			if err := tx.Create(member).Error; err != nil {
				return err
			}
		}

		// 在事务中发送邀请通知
		if err := sendTeamInviteMessageInTx(tx, wish, session.User.Nickname, in.UserId, member.ID); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "邀请失败，请稍后重试")
	}

	// 清除相关缓存（事务成功后清理）
	message_biz.ClearUserMessageCacheByTypes(in.UserId, []constmap.MessageType{constmap.MessageTypeTeam})

	// 9. 组装返回数据
	var out struct {
		WishId   uint `json:"wish_id"`
		UserId   uint `json:"user_id"`
		MemberId uint `json:"member_id"`
	}
	out.WishId = in.WishId
	out.UserId = in.UserId
	out.MemberId = member.ID

	return out, nil
}

// 在事务中发送组队邀请消息
func sendTeamInviteMessageInTx(tx *gorm.DB, wish *models.Wish, fromUserNickname string, toUserId, memberId uint) error {
	// 构建消息内容
	title := "心愿单组队邀请"
	content := fmt.Sprintf("%s 邀请您加入心愿单「%s」，一起去 %s", fromUserNickname, wish.Title, wish.To)

	// 创建消息模板
	messageTpl := &models.MessageTpl{
		Type:         constmap.MessageTypeTeam,
		SubType:      constmap.MessageSubTypeTeamInvite,
		Title:        title,
		Content:      content,
		WishMemberId: memberId,
	}

	if err := tx.Create(messageTpl).Error; err != nil {
		return err
	}

	// 创建用户消息记录（未读状态）
	message := &models.Message{
		MessageTplId: messageTpl.ID,
		UserId:       toUserId,
		IsRead:       constmap.Disable, // 未读
	}

	if err := tx.Create(message).Error; err != nil {
		return err
	}

	return nil
}
