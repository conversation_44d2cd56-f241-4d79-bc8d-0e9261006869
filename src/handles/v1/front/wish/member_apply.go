package wish

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/validators"
	"time"
)

// 申请同行人
func MemberApply(ctx *gin.Context) (any, error) {
	var in struct {
		WishId   uint   `form:"wish_id" binding:"required"`
		Phone    string `form:"phone" binding:"required"`
		RealName string `form:"real_name" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	// 校验手机号格式
	if !validators.IsMobile(in.Phone) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请输入正确的手机号码")
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	// 防并发处理
	mutexKey := fmt.Sprintf(constmap.RKMutex, "wishMemberApply", fmt.Sprintf("%d:%d", session.UserId, in.WishId))
	unlocker, err := my_cache.Mutex(mutexKey, constmap.TimeDur1m)
	if err != nil {
		return nil, utils.NewError(err)
	}
	defer unlocker()

	// 1. 检查心愿单是否存在
	wish := new(models.Wish)
	if err := db.Take(wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 2. 检查心愿单状态（只有进行中的心愿单才能申请）
	if wish.State != constmap.WishStateProcessing {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "该心愿单已结束或不可申请")
	}

	// 3. 检查心愿单截止时间（截止时间后不能申请）
	if time.Now().After(wish.Deadline) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "该心愿单已过申请截止时间")
	}

	// 4. 检查申请者身份（不能申请自己的心愿单）
	if wish.UserId == session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "不能申请自己发布的心愿单")
	}

	// 5. 检查是否已经申请过
	var existingMember models.WishMember
	err = db.Where(models.WishMember{
		WishId: in.WishId,
		UserId: session.UserId,
	}).Take(&existingMember).Error
	if err == nil {
		// 已存在申请记录
		switch existingMember.State {
		default:
			return nil, utils.NewErrorStr(constmap.ErrorParam, "请勿重复申请")
		case constmap.WishMemberStateWaitReview:
			return nil, utils.NewErrorStr(constmap.ErrorParam, "您已申请过该心愿单，请等待审核")
		case constmap.WishMemberStateApproved:
			return nil, utils.NewErrorStr(constmap.ErrorParam, "您已是该心愿单的成员")
		case constmap.WishMemberStateRejected:
			return nil, utils.NewErrorStr(constmap.ErrorParam, "您的申请已被拒绝")
		}
	}

	// 6. 使用事务创建申请记录和发送消息
	var member *models.WishMember
	err = db.Transaction(func(tx *gorm.DB) error {
		// 创建申请记录
		member = &models.WishMember{
			WishId:      in.WishId,
			UserId:      session.UserId,
			RealName:    in.RealName,
			Phone:       in.Phone,
			IsOwner:     constmap.Disable,                   // 非创建者
			State:       constmap.WishMemberStateWaitReview, // 默认待审核
			FollowState: constmap.WishFollowStateDefault,
		}

		if err := tx.Create(member).Error; err != nil {
			return err
		}

		// 向发起人发送组队申请消息
		if err := sendTeamApplyMessageInTx(tx, wish, session.User.Nickname, wish.UserId, member.ID); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "申请失败，请稍后重试")
	}

	// 清除相关缓存（事务成功后清理）
	message_biz.ClearUserMessageCacheByTypes(wish.UserId, []constmap.MessageType{constmap.MessageTypeTeam})

	var out struct {
		WishId   uint `json:"wish_id"`
		MemberId uint `json:"member_id"`
	}
	out.WishId = in.WishId
	out.MemberId = member.ID

	return out, nil
}

// 在事务中发送组队申请消息
func sendTeamApplyMessageInTx(tx *gorm.DB, wish *models.Wish, fromUserNickname string, toUserId, memberId uint) error {
	// 构建消息内容
	title := "心愿单组队申请"
	content := fmt.Sprintf("%s 申请加入您的心愿单「%s」，请及时处理", fromUserNickname, wish.Title)

	// 创建消息模板
	messageTpl := &models.MessageTpl{
		Type:         constmap.MessageTypeTeam,
		SubType:      constmap.MessageSubTypeTeamApply,
		Title:        title,
		Content:      content,
		WishMemberId: memberId,
	}

	if err := tx.Create(messageTpl).Error; err != nil {
		return err
	}

	// 创建用户消息记录（未读状态）
	message := &models.Message{
		MessageTplId: messageTpl.ID,
		UserId:       toUserId,
		IsRead:       constmap.Disable, // 未读
	}

	if err := tx.Create(message).Error; err != nil {
		return err
	}

	return nil
}
