package routers

import (
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	admin2 "roadtrip-api/src/handles/v1/admin"
	"roadtrip-api/src/handles/v1/front/account"
	"roadtrip-api/src/handles/v1/front/activity"
	"roadtrip-api/src/handles/v1/front/card"
	"roadtrip-api/src/handles/v1/front/chat"
	"roadtrip-api/src/handles/v1/front/hotel"
	"roadtrip-api/src/handles/v1/front/index"
	"roadtrip-api/src/handles/v1/front/lightbox"
	"roadtrip-api/src/handles/v1/front/message"
	"roadtrip-api/src/handles/v1/front/order"
	"roadtrip-api/src/handles/v1/front/pay"
	"roadtrip-api/src/handles/v1/front/pkg"
	"roadtrip-api/src/handles/v1/front/plan"
	"roadtrip-api/src/handles/v1/front/product"
	"roadtrip-api/src/handles/v1/front/scenic"
	"roadtrip-api/src/handles/v1/front/search"
	"roadtrip-api/src/handles/v1/front/tuan"
	"roadtrip-api/src/handles/v1/front/user"
	"roadtrip-api/src/handles/v1/front/user/user_people"
	"roadtrip-api/src/handles/v1/front/user/user_wish"
	"roadtrip-api/src/handles/v1/front/wechat"
	"roadtrip-api/src/handles/v1/front/wish"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

func front() {
	api()

	pa := config.Config.App.UploadDir
	engine.Static("/resources", pa+"/resources")
}

func api() {
	f := engine.Group("api/v1/front")
	f.Use(getFrontUser())
	{
		f.POST("screenshot", handle(index.Screenshot))
		f.POST("login", handle(index.Login))
		f.GET("geo", handle(index.Geo))
		f.GET("zones", handle(admin2.Zones))
		f.GET("cities", handle(index.Cities))
		f.GET("init", handle(index.Init))
		f.GET("index", handle(index.Index))
		f.GET("holiday", handle(index.Holiday))
		f.GET("station", handle(index.Station))
		f.GET("finder", handle(index.Finder)) //发现页
		f.POST("upload", handle(index.Upload))
		f.GET("third_rel_user", handle(index.ThirdRelUser))
		f.GET("plans", handle(index.Plans))
		f.GET("test", handle(index.Test))
		f.GET("qrcode", handle(index.Qrcode))

		fa := f.Group("")
		fa.Use(frontAuth())
		{
			fa.GET("upload_token", handle(index.UploadToken))
			fa.POST("like", handle(index.Like))
		}

		p1 := f.Group("pay")
		{
			p1.POST("notify/:method", handle(pay.Notify))
		}

		light := f.Group("lightbox")
		{
			light.GET("version", handle(lightbox.Version))
		}

		w := f.Group("wechat")
		{
			w.GET("access_token", handle(wechat.AccessToken))
		}

		p := f.Group("plan")
		{
			p.GET("history", handle(plan.History))
			p.GET("poster", handle(plan.Poster))
			p.GET("detail", handle(plan.Detail))
			p.GET("costdetail", handle(plan.CostDetail))
			p.GET("waymap_v2", handle(plan.WayMap2))
			p.GET("poster_index", handle(plan.PosterIndex))

			pu := p.Group("")
			pu.Use(frontAuth())

			pu.POST("poster_upload", handle(plan.PosterUpload))
			pu.POST("prompt", handle(plan.Prompt))
			pu.POST("like", handle(plan.Like))
			pu.GET("covers", handle(plan.Covers))
			pu.POST("submit", handle(plan.Submit))
			pu.GET("relate_tuan", handle(plan.RelateTuan))
			pu.POST("update", handle(plan.Update))
			pu.POST("pdf", handle(plan.Pdf))
		}

		o := f.Group("order")
		o.Use(frontAuth())
		{
			o.POST("preorder", handle(order.PreOrder))
			o.POST("payconfirm", handle(order.PayConfirm))
			o.POST("pay", handle(order.Pay))
			o.POST("submit", handle(order.Submit))
			o.GET("list", handle(order.List))
			o.GET("detail", handle(order.Detail))
			o.POST("cancel", handle(order.Cancel))
			o.GET("canrefunds", handle(order.CanRefunds))
			o.POST("applyrefund", handle(order.ApplyRefund))
			o.GET("refundlist", handle(order.RefundList))
			o.GET("refunddetail", handle(order.RefundDetail))
		}

		p2 := f.Group("tuan")
		{
			p2.GET("list", handle(tuan.List))
			p2.GET("dates", handle(tuan.Dates))
			p2.GET("detail", handle(tuan.Detail))
			p2.GET("option", handle(tuan.Option))
			p2.GET("skudates", handle(tuan.SkuDates))
			p2.Use(frontAuth())
			p2.POST("dotask", handle(tuan.DoTask))
			p2.POST("gettaskreward", handle(tuan.GetTaskReward))
			p2.GET("ordertaskinfo", handle(tuan.OrderTaskInfo))
		}

		s := f.Group("scenic")
		{
			s.GET("detail", handle(scenic.Detail))
			s.GET("search", handle(scenic.Search))
			s.GET("tickets", handle(scenic.Tickets))
		}

		c := f.Group("hotel")
		c.Use()
		{
			c.GET("detail", handle(hotel.Detail))
			c.GET("search", handle(hotel.Search))
			c.POST("remove", handle(hotel.Remove))
			c.POST("updatequantity", handle(hotel.UpdateQuantity))
			c.GET("roompolicy", handle(hotel.RoomPolicy))
		}

		ch := f.Group("search")
		{
			ch.GET("poi", handle(search.Poi)) // 聚合搜索接口
			ch.Use(frontAuth())
			ch.POST("prompt", handle(search.Prompt))
			ch.GET("init", handle(search.Init))
		}

		ca := f.Group("card")
		{
			ca.GET("got", frontAuth(), handle(card.Got))
			ca.POST("get", frontAuth(), handle(card.Get))
			ca.GET("info", handle(card.Info))
		}

		{
			p := f.Group("product")
			p.Use(frontAuth())
			p.GET("info", handle(product.Info))
			p.GET("calendar", handle(product.Calendar))
		}

		u := f.Group("user")
		u.Use(frontAuth())
		{
			u.POST("upprofile", handle(user.UpProfile))
			u.GET("center", handle(user.Center))
			u.GET("vipbenefit", handle(user.VipBenefit))
			u.POST("vippay", handle(user.VipPay))
			u.POST("pkgpay", handle(user.PkgPay))
			u.POST("logout", handle(user.Logout))
			u.POST("upload", handle(user.Upload))
			u.GET("planlist", handle(user.PlanList))
			u.POST("planremove", handle(user.PlanRemove))
			u.GET("wificards", handle(user.WifiCards))
			u.POST("bind_invite_code", handle(user.BindInviteCode))
			u.GET("ad_view_get", handle(user.AdViewGet))
			u.POST("ad_view_post", handle(user.AdViewPost))
			u.GET("activities", handle(user.Activities))

			uw := u.Group("wish")
			{
				uw.GET("list", handle(user_wish.List))
				uw.POST("close", handle(user_wish.Close))                // 关闭心愿单
				uw.POST("invite_member", handle(user_wish.InviteMember)) // 邀请成员
				uw.GET("member_list", handle(user_wish.MemberList))      // 成员列表
				uw.POST("kick_member", handle(user_wish.KickMember))     // 踢出成员
				uw.POST("set_success", handle(user_wish.SetSuccess))     // 标记心愿单已达成
				uw.POST("generate_plan", handle(user_wish.GeneratePlan)) // 生成行程
			}
			up := u.Group("people")
			{
				up.GET("list", handle(user_people.List))
				up.GET("options", handle(user_people.Options))
				up.POST("save", handle(user_people.Save))
				up.POST("del", handle(user_people.Del))
			}
		}
		cht := f.Group("chat")
		{
			cht.GET("list", handle(chat.List))
			cht.GET("prompt_stream", handle(chat.PromptStream))
			cht.Use(frontAuth())
			cht.POST("new_stream_chat", handle(chat.NewStreamChat))
			cht.GET("detail", handle(chat.Detail))
			cht.GET("index", handle(chat.Index))

		}
		act := f.Group("activity")
		{
			act.GET("detail", handle(activity.Detail))
			act.GET("poster", handle(activity.Poster))
			act.GET("week_pk_list", handle(activity.WeekPkList))
			act.GET("week_pk_settle_list", handle(activity.WeekPkSettleList))
			act.Use(frontAuth())
			act.POST("dotask", handle(activity.DoTask))
			act.POST("joinact", handle(activity.JoinAct))
			act.POST("getreward", handle(activity.GetReward))
			act.GET("tasklogs", handle(activity.TaskLogs))
		}
		pkgs := f.Group("pkg")
		{
			pkgs.GET("list", handle(pkg.List))
		}
		acct := f.Group("account")
		{
			acct.Use(frontAuth())
			acct.GET("loglist", handle(account.LogList))
		}

		wis := f.Group("wish")
		{
			wis.GET("index", handle(wish.Index))
			wis.GET("square_index", handle(wish.SquareIndex))
			wis.GET("squares", handle(wish.Squares))
			wis.GET("detail", handle(wish.Detail))
			wis.GET("comments", handle(wish.Comments))
			wis.Use(frontAuth())
			wis.GET("invite_list", handle(wish.InviteList))
			wis.GET("ai_suggest", handle(wish.AISuggest))
			wis.POST("submit", handle(wish.Submit))
			wis.POST("submit_comment", handle(wish.SubmitComment))
			wis.POST("comment_set_private", handle(wish.CommentSetPrivate))
			wis.POST("del_comment", handle(wish.DelComment))
			wis.POST("member_apply", handle(wish.MemberApply))
		}

		// 消息系统路由 - 需要登录验证
		msg := f.Group("message")
		{
			msg.GET("badge", handle(message.Badge)) // 消息徽章
			msg.GET("index", handle(message.Index)) // 消息汇总
			msg.Use(frontAuth())
			msg.GET("list", handle(message.List))                   // 消息列表
			msg.POST("set_read", handle(message.SetRead))           // 批量已读
			msg.POST("wish_invite", handle(message.WishInvite))     // 同意/拒绝心愿单邀请
			msg.POST("wish_apply", handle(message.WishApply))       // 同意/拒绝心愿单申请
			msg.POST("reply_comment", handle(message.ReplyComment)) // 快捷回复评论
		}
	}
}

func frontAuth() gin.HandlerFunc {
	return func(context *gin.Context) {
		start := time.Now()
		if session, err := business.GetFrontLoginUser(context); err != nil {
			response(start, context, nil, utils.NewErrorStr(constmap.ErrorNotLogin, constmap.ErrorMsgNotLogin))
			context.Abort()
		} else {
			if time.Now().Sub(session.UpdatedAt) > time.Minute*10 {
				db := utils.GetDB(context)

				updates := models.Session{
					UserId: session.UserId,
				}
				db.Model(&session).Omit(clause.Associations).Updates(updates)
			}

			context.Next()
		}
	}
}

func getFrontUser() gin.HandlerFunc {
	getSourceType := func(ctx *gin.Context) int {
		var in struct {
			Channel string `form:"channel"`
		}
		switch ctx.Request.Method {
		case "POST":
			_ = ctx.ShouldBindWith(&in, binding.Form)
		case "GET":
			_ = ctx.ShouldBindWith(&in, binding.Query)
		}
		switch in.Channel {
		case "weixin-h5":
			return constmap.SourceWeixinH5
		default:
			return constmap.SourceMpWeixin
		}
	}

	return func(ctx *gin.Context) {
		ctx.Set(constmap.ContextSource, getSourceType(ctx))
		token := ctx.GetHeader("token")
		if strutil.IsBlank(token) {
			token, _ = ctx.Cookie("token")
		}
		if strutil.IsBlank(token) {
			token = ctx.Query("token")
		}
		if !strutil.IsBlank(token) {
			var session models.Session
			db := utils.GetDB(ctx)

			if db.Where("token=? and User.id is not null", token).Joins("User").First(&session).Error != nil {
				return
			}

			ctx.Set(constmap.ContextFrontUser, &session)
		}
	}
}
